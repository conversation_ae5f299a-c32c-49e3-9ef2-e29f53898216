/**
 * Canvas State Protector
 * Safety net that detects canvas clearing and restores canvas state automatically
 * Part of Phase 2: Canvas State Protection for Royal Portrait Builder canvas clearing regression fix
 */

console.log('🛡️ Canvas State Protector Loading...');

/**
 * Canvas State Protector Class
 * Monitors canvas state and provides automatic restoration when clearing is detected
 */
class CanvasStateProtector {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.monitoringInterval = options.interval || 100; // 100ms monitoring
    this.backupInterval = options.backupInterval || 500; // 500ms backup
    this.layerBackup = null;
    this.isProtectionEnabled = true;
    this.clearingDetected = false;
    this.performanceMetrics = {
      monitoringTime: 0,
      backupTime: 0,
      restorationTime: 0,
      monitoringCount: 0,
      backupCount: 0,
      restorationCount: 0
    };
    
    console.log('🛡️ Canvas State Protector initialized', {
      monitoringInterval: this.monitoringInterval,
      backupInterval: this.backupInterval,
      canvas: this.canvas
    });
    
    this.startMonitoring();
  }

  /**
   * Start monitoring and backup timers
   */
  startMonitoring() {
    console.log('🛡️ Starting canvas state monitoring...');
    
    // Continuous state monitoring
    this.monitoringTimer = setInterval(() => {
      if (this.isProtectionEnabled) {
        this.checkCanvasState();
      }
    }, this.monitoringInterval);

    // Periodic backup
    this.backupTimer = setInterval(() => {
      if (this.isProtectionEnabled && this.hasImportantLayers()) {
        this.backupCanvasState();
      }
    }, this.backupInterval);
  }

  /**
   * Check canvas state for clearing detection
   */
  checkCanvasState() {
    const startTime = performance.now();
    
    try {
      const hasLayers = this.hasImportantLayers();
      const isCanvasCleared = this.isCanvasCleared();
      
      // Detect clearing: we had layers backed up, but now canvas is cleared and no layers exist
      if (!hasLayers && this.layerBackup && isCanvasCleared && !this.clearingDetected) {
        console.warn('🛡️ Canvas clearing detected, initiating restoration');
        this.clearingDetected = true;
        this.restoreCanvasState();
      } else if (hasLayers && this.clearingDetected) {
        // Reset clearing detection flag when layers are restored
        this.clearingDetected = false;
        console.log('🛡️ Canvas state normalized, clearing detection reset');
      }
      
      // Update performance metrics
      this.performanceMetrics.monitoringTime += performance.now() - startTime;
      this.performanceMetrics.monitoringCount++;
      
    } catch (error) {
      console.error('🛡️ Error during canvas state check:', error);
    }
  }

  /**
   * Check if canvas has important layers that should be preserved
   * @returns {boolean} True if canvas has important layers
   */
  hasImportantLayers() {
    return this.canvas.layers && (
      this.canvas.layers.breed ||
      this.canvas.layers.costume ||
      this.canvas.layers.frame ||
      this.canvas.layers.background
    );
  }

  /**
   * Check if canvas appears to be cleared (mostly white/transparent)
   * @returns {boolean} True if canvas appears cleared
   */
  isCanvasCleared() {
    if (!this.canvas.canvas) return true;
    
    try {
      const ctx = this.canvas.canvas.getContext('2d');
      const imageData = ctx.getImageData(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
      const data = imageData.data;
      
      // Check if canvas is mostly white/transparent
      let nonWhitePixels = 0;
      const totalPixels = data.length / 4;
      
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];
        
        // Count pixels that are not white/transparent
        if (!(r > 250 && g > 250 && b > 250) && a > 0) {
          nonWhitePixels++;
        }
      }
      
      // Canvas is considered cleared if less than 1% of pixels are non-white
      return nonWhitePixels < totalPixels * 0.01;
      
    } catch (error) {
      console.error('🛡️ Error checking canvas state:', error);
      return false;
    }
  }

  /**
   * Backup current canvas state
   */
  backupCanvasState() {
    const startTime = performance.now();
    
    try {
      this.layerBackup = {
        layers: JSON.parse(JSON.stringify(this.canvas.layers)),
        canvasData: this.canvas.canvas.toDataURL(),
        timestamp: Date.now()
      };
      
      console.log('💾 Canvas state backed up', {
        layers: Object.keys(this.layerBackup.layers).filter(key => this.layerBackup.layers[key]),
        timestamp: this.layerBackup.timestamp
      });
      
      // Update performance metrics
      this.performanceMetrics.backupTime += performance.now() - startTime;
      this.performanceMetrics.backupCount++;
      
    } catch (error) {
      console.error('🛡️ Error backing up canvas state:', error);
    }
  }

  /**
   * Restore canvas state from backup
   */
  async restoreCanvasState() {
    if (!this.layerBackup) {
      console.warn('🛡️ No backup available for restoration');
      return;
    }

    const startTime = performance.now();
    
    try {
      console.log('🔄 Restoring canvas state from backup');
      
      // Restore layer state
      this.canvas.layers = { ...this.layerBackup.layers };
      
      // Restore visual content
      const img = new Image();
      img.onload = () => {
        const ctx = this.canvas.canvas.getContext('2d');
        ctx.clearRect(0, 0, this.canvas.canvas.width, this.canvas.canvas.height);
        ctx.drawImage(img, 0, 0);
        console.log('✅ Canvas state restored successfully');
        
        // Update performance metrics
        this.performanceMetrics.restorationTime += performance.now() - startTime;
        this.performanceMetrics.restorationCount++;
        
        // Reset clearing detection flag after successful restoration
        setTimeout(() => {
          this.clearingDetected = false;
        }, 200);
      };
      
      img.onerror = (error) => {
        console.error('❌ Failed to load backup image for restoration:', error);
        this.clearingDetected = false;
      };
      
      img.src = this.layerBackup.canvasData;
      
    } catch (error) {
      console.error('❌ Failed to restore canvas state:', error);
      this.clearingDetected = false;
    }
  }

  /**
   * Get performance metrics
   * @returns {Object} Performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      averageMonitoringTime: this.performanceMetrics.monitoringCount > 0 
        ? this.performanceMetrics.monitoringTime / this.performanceMetrics.monitoringCount 
        : 0,
      averageBackupTime: this.performanceMetrics.backupCount > 0 
        ? this.performanceMetrics.backupTime / this.performanceMetrics.backupCount 
        : 0,
      averageRestorationTime: this.performanceMetrics.restorationCount > 0 
        ? this.performanceMetrics.restorationTime / this.performanceMetrics.restorationCount 
        : 0
    };
  }

  /**
   * Enable protection
   */
  enable() {
    this.isProtectionEnabled = true;
    console.log('🛡️ Canvas protection enabled');
  }

  /**
   * Disable protection
   */
  disable() {
    this.isProtectionEnabled = false;
    console.log('🛡️ Canvas protection disabled');
  }

  /**
   * Destroy protector and clean up timers
   */
  destroy() {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }
    
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
      this.backupTimer = null;
    }
    
    this.isProtectionEnabled = false;
    
    console.log('🛡️ Canvas State Protector destroyed', {
      performanceMetrics: this.getPerformanceMetrics()
    });
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { CanvasStateProtector };
} else {
  window.CanvasStateProtector = CanvasStateProtector;
}

console.log('🛡️ Canvas State Protector loaded successfully');
